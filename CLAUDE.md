# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React Native mobile application that enables SSH connections to remote Linux servers and provides AI coding assistance through Claude Code integration. The app allows users to browse remote file systems, view code with syntax highlighting, and interact with AI assistants for coding tasks.

## Development Commands

### Core Commands
- `pnpm start` - Start Expo development server
- `pnpm android` - Run on Android device/emulator
- `pnpm ios` - Run on iOS device/simulator
- `pnpm web` - Run in web browser
- `pnpm lint` - Run ESLint and Prettier checks
- `pnpm format` - Fix linting and formatting issues
- `pnpm test` - Run all tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:coverage` - Generate test coverage report

### Testing Specific Files
- `pnpm test src/__tests__/fileUtils.test.ts` - Test specific file
- `pnpm test ClaudeCodeAgent` - Test with pattern matching

## Architecture

### Core Architecture Pattern
The app follows a layered architecture with clear separation of concerns:

1. **Service Layer** (`src/services/`)
   - `SSHService.ts` - Manages SSH connections and command execution
   - `agents/` - AI assistant implementations using factory pattern

2. **Component Layer** (`src/components/`)
   - `FileSystemBrowser.tsx` - Remote file system navigation
   - `CodeViewer.tsx` - Code viewing with syntax highlighting
   - `AIAssistantChat.tsx` - AI chat interface
   - `SSHConnectionModal.tsx` - Connection configuration

3. **State Management** (`src/hooks/`)
   - `useAppState.ts` - Centralized application state

4. **Utilities** (`src/utils/`)
   - `fileUtils.ts` - File type detection and processing

### Key Technical Decisions
- **State Management**: React Hooks with centralized state
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **SSH**: react-native-ssh-sftp (with simulation fallback)
- **Testing**: Jest with TypeScript support
- **Navigation**: Stack-based navigation (Expo Router ready)

## Key Files and Their Purpose

### Services
- `src/services/SSHService.ts:12` - SSH connection management with mock fallbacks
- `src/services/agents/BaseAgent.ts:4` - Abstract base class for AI agents
- `src/services/agents/ClaudeCodeAgent.ts:4` - Claude Code specific implementation
- `src/services/agents/AgentFactory.ts` - Factory pattern for agent creation

### Components
- `src/components/FileSystemBrowser.tsx` - File/directory browser with icons
- `src/components/CodeViewer.tsx` - Code viewer with line selection and syntax highlighting
- `src/components/AIAssistantChat.tsx` - Real-time AI chat interface
- `src/components/SSHConnectionModal.tsx` - SSH connection configuration form

### Types and Utilities
- `src/types/index.ts` - Centralized TypeScript interfaces and enums
- `src/utils/fileUtils.ts` - File type detection and icon mapping

## Development Flow

### Adding New AI Agents
1. Extend `BaseAgent` in `src/services/agents/`
2. Implement required abstract methods
3. Register in `AgentFactory.ts`
4. Add new type to `AIAssistantType` enum

### Adding File Type Support
1. Update icon mapping in `fileUtils.ts`
2. Add syntax highlighting support in `CodeViewer.tsx`
3. Update test cases

### Testing Strategy
- Unit tests for utilities and services in `src/__tests__/`
- Component tests using React Native Testing Library
- Mock SSH responses for consistent testing

## Common Development Tasks

### SSH Service Testing
Since the app uses simulated SSH responses during development, to test with real SSH:
1. Install react-native-ssh-sftp properly
2. Replace mock implementations in SSHService
3. Update connection handling for production

### Code Context Features
The app supports code selection and context passing to AI assistants:
- Line-based selection in `CodeViewer.tsx`
- Context formatting in `BaseAgent.ts:52`
- File path association for better AI responses

### Styling System
Uses NativeWind with custom dark theme defined in `tailwind.config.js:9-22`:
- Dark mode by default
- Consistent color tokens
- Responsive design patterns

## Testing Patterns

### Mocking SSH Commands
Tests use simulated responses. For real testing:
```typescript
// In SSHService.test.ts
jest.mock('react-native-ssh-sftp', () => ({
  // Mock implementation
}));
```

### Agent Testing
- Test agent lifecycle (start/stop)
- Message formatting and context handling
- Error conditions and recovery