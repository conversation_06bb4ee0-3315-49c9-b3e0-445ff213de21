# 远程编码助手 (Remote Coding Assistant)

一个基于React Native和Expo的移动应用，允许用户通过SSH连接到远程Linux服务器，并与AI编码助手（如Claude Code）进行交互式编程。

## 🚀 核心功能

### 1. SSH连接管理
- 支持密码和私钥认证
- 连接状态实时监控
- 自动重连机制
- 连接历史记录

### 2. 远程文件系统浏览
- 直观的文件和目录浏览界面
- 支持多种文件类型图标显示
- 文件大小和权限信息展示
- 快速导航功能

### 3. 代码查看器
- 语法高亮支持多种编程语言
- 代码行号显示
- 类似Cursor的代码框选功能
- 支持代码片段选择和上下文提取

### 4. AI助手集成
- 抽象的Agent架构，支持多种AI助手
- 优先支持Claude Code集成
- 实时消息交互界面
- 代码上下文感知对话
- 消息历史记录和持久化

### 5. 移动端优化
- 响应式设计适配不同屏幕尺寸
- 手势操作支持
- 直观的用户界面
- 流畅的交互体验

## 🏗️ 技术架构

### 前端技术栈
- **React Native**: 跨平台移动应用开发
- **Expo**: 开发工具链和运行时
- **TypeScript**: 类型安全的JavaScript
- **NativeWind**: Tailwind CSS for React Native
- **React Native Paper**: Material Design组件库

### 核心模块

#### SSH服务层 (`src/services/SSHService.ts`)
- 管理SSH连接生命周期
- 执行远程命令
- 文件系统操作
- 连接状态管理

#### Agent抽象层 (`src/services/agents/`)
- `BaseAgent`: 抽象基类定义AI助手接口
- `ClaudeCodeAgent`: Claude Code具体实现
- `AgentFactory`: Agent工厂模式，支持扩展

#### 组件层 (`src/components/`)
- `FileSystemBrowser`: 文件系统浏览器
- `CodeViewer`: 代码查看和选择器
- `AIAssistantChat`: AI助手交互界面
- `SSHConnectionModal`: SSH连接配置

#### 工具层 (`src/utils/`)
- `fileUtils`: 文件类型识别和处理
- 类型定义 (`src/types/`)

## 📱 使用方法

### 1. 安装依赖
```bash
npx pnpm install
```

### 2. 启动开发服务器
```bash
npx pnpm start
```

### 3. 在移动设备上运行
- 使用Expo Go扫描二维码
- 或者使用iOS模拟器/Android模拟器

### 4. 连接远程服务器
1. 点击"连接"按钮
2. 填写SSH连接信息（主机、用户名、密码/私钥）
3. 建立连接后即可浏览文件系统

### 5. 启动AI助手
1. 确保远程服务器已安装Claude Code
2. 点击"启动"AI助手
3. 在聊天界面与AI进行交互

### 6. 代码上下文交互
1. 在文件浏览器中选择代码文件
2. 使用代码查看器选择特定代码片段
3. 选中的代码将作为上下文发送给AI助手

## 🧪 测试

### 运行测试
```bash
npx pnpm test
```

### 运行特定测试
```bash
npx pnpm test src/__tests__/fileUtils.test.ts
```

### 测试覆盖率
```bash
npx pnpm test:coverage
```

## 🔧 开发

### 项目结构
```
src/
├── components/          # React组件
├── services/           # 业务逻辑服务
│   └── agents/        # AI助手实现
├── hooks/             # React Hooks
├── utils/             # 工具函数
├── types/             # TypeScript类型定义
├── screens/           # 屏幕组件
└── __tests__/         # 测试文件
```

### 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- TypeScript严格模式
- 组件和函数需要适当的类型注解

### 格式化代码
```bash
npx pnpm format
```

### 检查代码
```bash
npx pnpm lint
```

## 🚧 扩展性

### 添加新的AI助手
1. 继承`BaseAgent`类
2. 实现必要的抽象方法
3. 在`AgentFactory`中注册新的Agent
4. 更新类型定义

### 添加新的文件类型支持
1. 更新`fileUtils.ts`中的映射表
2. 添加相应的图标和语言支持
3. 更新测试用例

## 📋 待办事项

- [ ] 实现Gemini CLI Agent
- [ ] 添加文件编辑功能
- [ ] 支持多个SSH连接
- [ ] 添加代码搜索功能
- [ ] 实现文件上传/下载
- [ ] 添加主题切换
- [ ] 支持更多编程语言的语法高亮

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
