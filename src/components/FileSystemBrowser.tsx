import React, { useState, useEffect } from 'react';
import {
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Text as UIText } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';
import { getFileIcon, formatFileSize } from '../utils/fileUtils';

interface FileSystemBrowserProps {
  sshService: SSHService;
  onFileSelect: (file: FileSystemItem) => void;
  onDirectoryChange: (path: string) => void;
  currentPath: string;
}

export const FileSystemBrowser: React.FC<FileSystemBrowserProps> = ({
  sshService,
  onFileSelect,
  onDirectoryChange,
  currentPath,
}) => {
  const [items, setItems] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = async (path: string) => {
    if (!sshService.isConnected()) {
      setError('SSH连接未建立');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const directoryItems = await sshService.listDirectory(path);
      setItems(directoryItems);
    } catch (err) {
      setError(`加载目录失败: ${err}`);
      Alert.alert('错误', `无法加载目录: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (item: FileSystemItem) => {
    if (item.type === 'directory') {
      onDirectoryChange(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    onDirectoryChange(parentPath);
  };

  const getItemIcon = (item: FileSystemItem) => {
    return getFileIcon(item.name, item.type === 'directory');
  };

  const renderItem = ({ item }: { item: FileSystemItem }) => (
    <Pressable
      className="bg-background-50 mx-3 my-0.5 rounded-lg border border-outline-200"
      onPress={() => handleItemPress(item)}
    >
      <HStack className="items-center p-3">
        <UIText className="text-2xl mr-3">{getItemIcon(item)}</UIText>
        <VStack className="flex-1">
          <UIText className="text-base font-medium text-typography-900 mb-1">{item.name}</UIText>
          <HStack className="justify-between">
            <UIText className="text-xs text-typography-600">{formatFileSize(item.size)}</UIText>
            <UIText className="text-xs text-typography-500 font-mono">{item.permissions}</UIText>
          </HStack>
        </VStack>
      </HStack>
    </Pressable>
  );

  if (loading) {
    return (
      <Box className="flex-1 justify-center items-center bg-background-0">
        <ActivityIndicator size="large" color="#4ade80" />
        <UIText className="mt-3 text-base text-typography-600">加载中...</UIText>
      </Box>
    );
  }

  return (
    <Box className="flex-1 bg-background-0">
      {/* 路径导航 */}
      <VStack className="bg-background-50 p-3 border-b border-outline-200">
        <Button
          className="bg-primary-500 px-3 py-1.5 self-start mb-2"
          onPress={navigateUp}
        >
          <ButtonText className="text-typography-0 text-sm font-medium">上级目录</ButtonText>
        </Button>
        <UIText className="text-sm text-typography-600 font-mono">{currentPath}</UIText>
      </VStack>

      {/* 错误提示 */}
      {error && (
        <Box className="bg-error-50 border border-error-200 p-3 m-3 rounded-md">
          <UIText className="text-error-600 text-sm">{error}</UIText>
        </Box>
      )}

      {/* 文件列表 */}
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.path}
        className="flex-1"
        showsVerticalScrollIndicator={false}
      />
    </Box>
  );
};


