import React, { useState } from 'react';
import {
  TextInput,
  Modal,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Text as UIText } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { SSHConfig } from '../types';

interface SSHConnectionModalProps {
  visible: boolean;
  onConnect: (config: SSHConfig) => void;
  onCancel: () => void;
}

export const SSHConnectionModal: React.FC<SSHConnectionModalProps> = ({
  visible,
  onConnect,
  onCancel,
}) => {
  const [host, setHost] = useState('');
  const [port, setPort] = useState('22');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [usePrivateKey, setUsePrivateKey] = useState(false);
  const [privateKey, setPrivateKey] = useState('');
  const [passphrase, setPassphrase] = useState('');

  const handleConnect = () => {
    if (!host.trim() || !username.trim()) {
      Alert.alert('错误', '请填写主机地址和用户名');
      return;
    }

    if (!usePrivateKey && !password.trim()) {
      Alert.alert('错误', '请填写密码或选择使用私钥');
      return;
    }

    if (usePrivateKey && !privateKey.trim()) {
      Alert.alert('错误', '请填写私钥内容');
      return;
    }

    const config: SSHConfig = {
      host: host.trim(),
      port: parseInt(port) || 22,
      username: username.trim(),
      password: usePrivateKey ? undefined : password,
      privateKey: usePrivateKey ? privateKey.trim() : undefined,
      passphrase: passphrase.trim() || undefined,
    };

    onConnect(config);
  };

  const resetForm = () => {
    setHost('');
    setPort('22');
    setUsername('');
    setPassword('');
    setUsePrivateKey(false);
    setPrivateKey('');
    setPassphrase('');
  };

  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        className="flex-1 bg-background-0"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <HStack className="items-center justify-between bg-background-50 px-4 py-3 border-b border-outline-200">
          <Pressable className="p-2" onPress={handleCancel}>
            <UIText className="text-primary-600 text-base">取消</UIText>
          </Pressable>
          <UIText className="text-lg font-bold text-typography-950">SSH连接</UIText>
          <Button
            className="bg-primary-500 px-4 py-2"
            onPress={handleConnect}
          >
            <ButtonText className="text-typography-0 text-base font-medium">连接</ButtonText>
          </Button>
        </HStack>

        <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
          {/* 主机地址 */}
          <VStack className="mb-5">
            <UIText className="text-base font-medium text-typography-900 mb-2">主机地址 *</UIText>
            <TextInput
              className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900"
              value={host}
              onChangeText={setHost}
              placeholder="例如: *************"
              placeholderTextColor="#9CA3AF"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </VStack>

          {/* 端口 */}
          <VStack className="mb-5">
            <UIText className="text-base font-medium text-typography-900 mb-2">端口</UIText>
            <TextInput
              className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900"
              value={port}
              onChangeText={setPort}
              placeholder="22"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </VStack>

          {/* 用户名 */}
          <VStack className="mb-5">
            <UIText className="text-base font-medium text-typography-900 mb-2">用户名 *</UIText>
            <TextInput
              className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900"
              value={username}
              onChangeText={setUsername}
              placeholder="例如: root"
              placeholderTextColor="#9CA3AF"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </VStack>

          {/* 认证方式选择 */}
          <HStack className="mb-5 bg-background-50 border border-outline-200 rounded-lg p-1">
            <Pressable
              className={`flex-1 py-3 items-center rounded-md ${
                !usePrivateKey ? 'bg-primary-500' : 'bg-transparent'
              }`}
              onPress={() => setUsePrivateKey(false)}
            >
              <UIText className={`text-base ${
                !usePrivateKey ? 'text-typography-0 font-medium' : 'text-typography-600'
              }`}>
                密码认证
              </UIText>
            </Pressable>
            <Pressable
              className={`flex-1 py-3 items-center rounded-md ${
                usePrivateKey ? 'bg-primary-500' : 'bg-transparent'
              }`}
              onPress={() => setUsePrivateKey(true)}
            >
              <UIText className={`text-base ${
                usePrivateKey ? 'text-typography-0 font-medium' : 'text-typography-600'
              }`}>
                私钥认证
              </UIText>
            </Pressable>
          </HStack>

          {/* 密码认证 */}
          {!usePrivateKey && (
            <VStack className="mb-5">
              <UIText className="text-base font-medium text-typography-900 mb-2">密码 *</UIText>
              <TextInput
                className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900"
                value={password}
                onChangeText={setPassword}
                placeholder="请输入密码"
                placeholderTextColor="#9CA3AF"
                secureTextEntry
              />
            </VStack>
          )}

          {/* 私钥认证 */}
          {usePrivateKey && (
            <>
              <VStack className="mb-5">
                <UIText className="text-base font-medium text-typography-900 mb-2">私钥内容 *</UIText>
                <TextInput
                  className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900 h-30"
                  style={{ textAlignVertical: 'top' }}
                  value={privateKey}
                  onChangeText={setPrivateKey}
                  placeholder="请粘贴私钥内容（PEM格式）"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={6}
                />
              </VStack>

              <VStack className="mb-5">
                <UIText className="text-base font-medium text-typography-900 mb-2">私钥密码（可选）</UIText>
                <TextInput
                  className="border border-outline-300 rounded-lg px-3 py-3 text-base bg-background-0 text-typography-900"
                  value={passphrase}
                  onChangeText={setPassphrase}
                  placeholder="如果私钥有密码请输入"
                  placeholderTextColor="#9CA3AF"
                  secureTextEntry
                />
              </VStack>
            </>
          )}

          {/* 连接提示 */}
          <Box className="bg-info-100 border border-info-200 p-3 rounded-lg mt-5">
            <UIText className="text-sm text-info-700 leading-5">
              提示：确保目标服务器已安装Claude Code，并且您有足够的权限访问项目目录。
            </UIText>
          </Box>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};


