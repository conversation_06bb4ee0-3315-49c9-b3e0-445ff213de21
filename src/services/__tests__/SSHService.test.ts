import { SSHService } from '../SSHService';
import { SSHConfig, ConnectionStatus } from '../../types';

// Mock the SSH client
jest.mock('@dylankenneally/react-native-ssh-sftp', () => ({
  __esModule: true,
  default: {
    connectWithPassword: jest.fn(),
    connectWithKey: jest.fn(),
  },
}));

// Mock PlatformDetector
jest.mock('../../utils/PlatformDetector', () => ({
  __esModule: true,
  default: {
    getInstance: jest.fn(() => ({
      getPlatformInfo: jest.fn(() => ({
        isIOS: false,
        isAndroid: true,
        isIOSSimulator: false,
        isIOSDevice: false,
        supportsSSH: true
      })),
      isSSHSupported: jest.fn(() => true),
      logPlatformInfo: jest.fn(),
    })),
  },
}));

describe('SSHService', () => {
  let sshService: SSHService;
  let mockClient: any;

  beforeEach(() => {
    sshService = new SSHService();
    mockClient = {
      execute: jest.fn(),
      disconnect: jest.fn(),
      connectSFTP: jest.fn(),
      disconnectSFTP: jest.fn(),
      sftpLs: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('connect', () => {
    it('should connect with password successfully', async () => {
      const SSHClient = require('@dylankenneally/react-native-ssh-sftp').default;
      SSHClient.connectWithPassword.mockResolvedValue(mockClient);

      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser',
        password: 'testpass'
      };

      await sshService.connect(config);

      expect(SSHClient.connectWithPassword).toHaveBeenCalledWith(
        'test.example.com',
        22,
        'testuser',
        'testpass'
      );
      expect(sshService.getStatus()).toBe(ConnectionStatus.CONNECTED);
      expect(sshService.isConnected()).toBe(true);
    });

    it('should connect with private key successfully', async () => {
      const SSHClient = require('@dylankenneally/react-native-ssh-sftp').default;
      SSHClient.connectWithKey.mockResolvedValue(mockClient);

      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser',
        privateKey: '-----BEGIN RSA PRIVATE KEY-----\ntest\n-----END RSA PRIVATE KEY-----',
        passphrase: 'keypass'
      };

      await sshService.connect(config);

      expect(SSHClient.connectWithKey).toHaveBeenCalledWith(
        'test.example.com',
        22,
        'testuser',
        '-----BEGIN RSA PRIVATE KEY-----\ntest\n-----END RSA PRIVATE KEY-----',
        'keypass'
      );
      expect(sshService.getStatus()).toBe(ConnectionStatus.CONNECTED);
    });

    it('should throw error when neither password nor private key provided', async () => {
      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser'
      };

      await expect(sshService.connect(config)).rejects.toThrow(
        'Either password or private key must be provided'
      );
    });

    it('should handle connection errors', async () => {
      const SSHClient = require('@dylankenneally/react-native-ssh-sftp').default;
      SSHClient.connectWithPassword.mockRejectedValue(new Error('Connection failed'));

      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser',
        password: 'testpass'
      };

      await expect(sshService.connect(config)).rejects.toThrow('Connection failed');
      expect(sshService.getStatus()).toBe(ConnectionStatus.ERROR);
    });
  });

  describe('executeCommand', () => {
    beforeEach(async () => {
      const SSHClient = require('@dylankenneally/react-native-ssh-sftp').default;
      SSHClient.connectWithPassword.mockResolvedValue(mockClient);

      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser',
        password: 'testpass'
      };

      await sshService.connect(config);
    });

    it('should execute command successfully', async () => {
      mockClient.execute.mockResolvedValue('command output');

      const result = await sshService.executeCommand('ls -la');

      expect(mockClient.execute).toHaveBeenCalledWith('ls -la');
      expect(result).toBe('command output');
    });

    it('should handle empty command output', async () => {
      mockClient.execute.mockResolvedValue('');

      const result = await sshService.executeCommand('ls -la');

      expect(result).toBe('');
    });

    it('should throw error when not connected', async () => {
      await sshService.disconnect();

      await expect(sshService.executeCommand('ls -la')).rejects.toThrow(
        'SSH connection not established'
      );
    });
  });

  describe('disconnect', () => {
    it('should disconnect successfully', async () => {
      const SSHClient = require('@dylankenneally/react-native-ssh-sftp').default;
      SSHClient.connectWithPassword.mockResolvedValue(mockClient);

      const config: SSHConfig = {
        host: 'test.example.com',
        port: 22,
        username: 'testuser',
        password: 'testpass'
      };

      await sshService.connect(config);
      await sshService.disconnect();

      expect(mockClient.disconnect).toHaveBeenCalled();
      expect(sshService.getStatus()).toBe(ConnectionStatus.DISCONNECTED);
      expect(sshService.isConnected()).toBe(false);
    });
  });
});
