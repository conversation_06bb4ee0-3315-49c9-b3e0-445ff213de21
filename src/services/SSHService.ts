import { SSHConfig, ConnectionStatus, FileSystemItem } from '../types';

export class SSHService {
  private connection: any = null;
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
  private statusCallbacks: ((status: ConnectionStatus) => void)[] = [];

  constructor() {
    // 初始化SSH服务
  }

  async connect(config: SSHConfig): Promise<void> {
    try {
      this.setStatus(ConnectionStatus.CONNECTING);
      
      // 这里需要使用实际的SSH库进行连接
      // 由于react-native-ssh-sftp可能有兼容性问题，我们先创建一个模拟实现
      
      // 模拟连接延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟连接成功
      this.connection = {
        config,
        connected: true
      };
      
      this.setStatus(ConnectionStatus.CONNECTED);
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      // 关闭连接
      this.connection = null;
    }
    this.setStatus(ConnectionStatus.DISCONNECTED);
  }

  async executeCommand(command: string): Promise<string> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    try {
      // 模拟命令执行
      console.log(`Executing command: ${command}`);
      
      // 这里应该使用实际的SSH连接执行命令
      // 现在返回模拟结果
      if (command.startsWith('ls')) {
        return this.getMockFileList(command);
      } else if (command.startsWith('cat')) {
        return this.getMockFileContent(command);
      } else if (command.includes('claude-code')) {
        return 'Claude Code started successfully';
      }
      
      return `Command executed: ${command}`;
    } catch (error) {
      throw new Error(`Command execution failed: ${error}`);
    }
  }

  async listDirectory(path: string): Promise<FileSystemItem[]> {
    const command = `ls -la "${path}"`;
    const output = await this.executeCommand(command);
    return this.parseFileList(output, path);
  }

  async readFile(filePath: string): Promise<string> {
    const command = `cat "${filePath}"`;
    return await this.executeCommand(command);
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    const command = `echo '${content.replace(/'/g, "'\\''")}' > "${filePath}"`;
    await this.executeCommand(command);
  }

  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED && this.connection !== null;
  }

  getStatus(): ConnectionStatus {
    return this.status;
  }

  onStatusChange(callback: (status: ConnectionStatus) => void): void {
    this.statusCallbacks.push(callback);
  }

  private setStatus(status: ConnectionStatus): void {
    this.status = status;
    this.statusCallbacks.forEach(callback => callback(status));
  }

  private getMockFileList(command: string): string {
    // 模拟ls命令输出
    return `total 24
drwxr-xr-x  5 <USER> <GROUP> 4096 Jan 15 10:30 .
drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 15 10:00 ..
-rw-r--r--  1 <USER> <GROUP>  156 Jan 15 10:30 README.md
drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 15 10:25 src
-rw-r--r--  1 <USER> <GROUP>  523 Jan 15 10:20 package.json
-rw-r--r--  1 <USER> <GROUP> 1024 Jan 15 10:15 main.py`;
  }

  private getMockFileContent(command: string): string {
    const fileName = command.split(' ').pop();
    if (fileName?.includes('README.md')) {
      return '# Remote Coding Project\n\nThis is a sample project for remote coding.';
    } else if (fileName?.includes('main.py')) {
      return `#!/usr/bin/env python3
def main():
    print("Hello, World!")
    
if __name__ == "__main__":
    main()`;
    } else if (fileName?.includes('package.json')) {
      return `{
  "name": "remote-project",
  "version": "1.0.0",
  "main": "index.js"
}`;
    }
    return 'File content not found';
  }

  private parseFileList(output: string, basePath: string): FileSystemItem[] {
    const lines = output.split('\n').filter(line => line.trim() && !line.startsWith('total'));
    const items: FileSystemItem[] = [];

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length < 9) continue;

      const permissions = parts[0];
      const size = parseInt(parts[4]) || 0;
      const name = parts.slice(8).join(' ');
      
      if (name === '.' || name === '..') continue;

      const type = permissions.startsWith('d') ? 'directory' : 'file';
      const path = `${basePath}/${name}`.replace(/\/+/g, '/');

      items.push({
        name,
        path,
        type,
        size,
        permissions,
        modifiedTime: new Date()
      });
    }

    return items;
  }
}
