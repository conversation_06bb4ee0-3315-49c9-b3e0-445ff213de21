import { SSHConfig, ConnectionStatus, FileSystemItem } from '../types';
import SSHClient from '@dylankenneally/react-native-ssh-sftp';
import { PlatformDetector } from '../utils/PlatformDetector';

export class SSHService {
  private client: any = null;
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
  private statusCallbacks: ((status: ConnectionStatus) => void)[] = [];
  private platformDetector: PlatformDetector;
  private sftpConnected: boolean = false;

  constructor() {
    this.platformDetector = PlatformDetector.getInstance();
    this.platformDetector.logPlatformInfo();
  }

  async connect(config: SSHConfig): Promise<void> {
    try {
      this.setStatus(ConnectionStatus.CONNECTING);

      // 使用真实的SSH连接
      if (config.password) {
        this.client = await SSHClient.connectWithPassword(
          config.host,
          config.port,
          config.username,
          config.password
        );
      } else if (config.privateKey) {
        this.client = await SSHClient.connectWithKey(
          config.host,
          config.port,
          config.username,
          config.privateKey,
          config.passphrase
        );
      } else {
        throw new Error('Either password or private key must be provided');
      }

      this.setStatus(ConnectionStatus.CONNECTED);
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.client) {
        // 断开SFTP连接（如果已连接）
        if (this.sftpConnected) {
          await this.client.disconnectSFTP();
          this.sftpConnected = false;
        }

        // 断开SSH连接
        await this.client.disconnect();
        this.client = null;
      }
    } catch (error) {
      console.warn('Error during disconnect:', error);
    } finally {
      this.setStatus(ConnectionStatus.DISCONNECTED);
    }
  }

  async executeCommand(command: string): Promise<string> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    try {
      console.log(`Executing command: ${command}`);

      // 使用真实的SSH连接执行命令
      const result = await this.client.execute(command);
      return result || '';
    } catch (error) {
      throw new Error(`Command execution failed: ${error}`);
    }
  }

  async listDirectory(path: string): Promise<FileSystemItem[]> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    try {
      // 优先使用SFTP方式列出目录
      await this.ensureSFTPConnection();
      const items = await this.client.sftpLs(path);
      return this.parseSFTPFileList(items, path);
    } catch (error) {
      // 如果SFTP失败，回退到命令行方式
      console.warn('SFTP list failed, falling back to command:', error);
      const command = `ls -la "${path}"`;
      const output = await this.executeCommand(command);
      return this.parseFileList(output, path);
    }
  }

  async readFile(filePath: string): Promise<string> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    // 对于文件读取，使用命令行方式更可靠
    const command = `cat "${filePath}"`;
    return await this.executeCommand(command);
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    // 对于文件写入，使用命令行方式
    const escapedContent = content.replace(/'/g, "'\\''");
    const command = `echo '${escapedContent}' > "${filePath}"`;
    await this.executeCommand(command);
  }

  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED && this.client !== null;
  }

  getStatus(): ConnectionStatus {
    return this.status;
  }

  onStatusChange(callback: (status: ConnectionStatus) => void): void {
    this.statusCallbacks.push(callback);
  }

  private setStatus(status: ConnectionStatus): void {
    this.status = status;
    this.statusCallbacks.forEach(callback => callback(status));
  }



  // SFTP相关方法
  private async ensureSFTPConnection(): Promise<void> {
    if (!this.sftpConnected && this.client) {
      await this.client.connectSFTP();
      this.sftpConnected = true;
    }
  }

  private parseSFTPFileList(items: any[], basePath: string): FileSystemItem[] {
    const fileItems: FileSystemItem[] = [];

    for (const item of items) {
      if (item.filename === '.' || item.filename === '..') continue;

      const path = `${basePath}/${item.filename}`.replace(/\/+/g, '/');
      const type = item.isDirectory ? 'directory' : 'file';

      fileItems.push({
        name: item.filename,
        path,
        type,
        size: item.fileSize || 0,
        permissions: item.permissions || '',
        modifiedTime: item.modifiedDate ? new Date(item.modifiedDate) : new Date()
      });
    }

    return fileItems;
  }



  private parseFileList(output: string, basePath: string): FileSystemItem[] {
    const lines = output.split('\n').filter(line => line.trim() && !line.startsWith('total'));
    const items: FileSystemItem[] = [];

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length < 9) continue;

      const permissions = parts[0];
      const size = parseInt(parts[4]) || 0;
      const name = parts.slice(8).join(' ');
      
      if (name === '.' || name === '..') continue;

      const type = permissions.startsWith('d') ? 'directory' : 'file';
      const path = `${basePath}/${name}`.replace(/\/+/g, '/');

      items.push({
        name,
        path,
        type,
        size,
        permissions,
        modifiedTime: new Date()
      });
    }

    return items;
  }
}
