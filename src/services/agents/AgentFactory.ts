import { AIAgent, AIAssistantType } from '../../types';
import { SSHService } from '../SSHService';
import { ClaudeCodeAgent } from './ClaudeCodeAgent';
import { BaseAgent } from './BaseAgent';

// 未来的Gemini CLI Agent实现
class GeminiCliAgent extends BaseAgent {
  async start(config: any): Promise<void> {
    // TODO: 实现Gemini CLI启动逻辑
    throw new Error('Gemini CLI Agent not implemented yet');
  }

  async stop(): Promise<void> {
    // TODO: 实现Gemini CLI停止逻辑
    throw new Error('Gemini CLI Agent not implemented yet');
  }

  async sendMessage(message: string, codeContext?: any, filePath?: string): Promise<string> {
    // TODO: 实现Gemini CLI消息发送逻辑
    throw new Error('Gemini CLI Agent not implemented yet');
  }
}

export class AgentFactory {
  private static agents: Map<AIAssistantType, new (sshService: SSHService) => AIAgent> = new Map([
    [AIAssistantType.CLAUDE_CODE, ClaudeCodeAgent],
    [AIAssistantType.GEMINI_CLI, GeminiCliAgent]
  ]);

  static createAgent(type: AIAssistantType, sshService: SSHService): AIAgent {
    const AgentClass = this.agents.get(type);
    
    if (!AgentClass) {
      throw new Error(`Unsupported AI assistant type: ${type}`);
    }

    return new AgentClass(sshService);
  }

  static getSupportedTypes(): AIAssistantType[] {
    return Array.from(this.agents.keys());
  }

  static isSupported(type: AIAssistantType): boolean {
    return this.agents.has(type);
  }

  static registerAgent(type: AIAssistantType, agentClass: new (sshService: SSHService) => AIAgent): void {
    this.agents.set(type, agentClass);
  }
}
