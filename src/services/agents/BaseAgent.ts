import { <PERSON><PERSON><PERSON>, AIAssistantConfig, AIAssistantStatus, AIMessage, CodeSelection } from '../../types';
import { SSHService } from '../SSHService';

export abstract class BaseAgent implements AIAgent {
  protected sshService: SSHService;
  protected status: AIAssistantStatus = AIAssistantStatus.IDLE;
  protected config?: AIAssistantConfig;
  protected statusCallbacks: ((status: AIAssistantStatus) => void)[] = [];
  protected messageCallbacks: ((message: AIMessage) => void)[] = [];
  protected processId?: string;

  constructor(sshService: SSHService) {
    this.sshService = sshService;
  }

  abstract start(config: AIAssistantConfig): Promise<void>;
  abstract stop(): Promise<void>;
  abstract sendMessage(message: string, codeContext?: CodeSelection, filePath?: string): Promise<string>;

  getStatus(): AIAssistantStatus {
    return this.status;
  }

  onStatusChange(callback: (status: AIAssistantStatus) => void): void {
    this.statusCallbacks.push(callback);
  }

  onMessage(callback: (message: AIMessage) => void): void {
    this.messageCallbacks.push(callback);
  }

  protected setStatus(status: AIAssistantStatus): void {
    this.status = status;
    this.statusCallbacks.forEach(callback => callback(status));
  }

  protected emitMessage(message: AIMessage): void {
    this.messageCallbacks.forEach(callback => callback(message));
  }

  protected generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  protected async executeCommand(command: string): Promise<string> {
    if (!this.sshService.isConnected()) {
      throw new Error('SSH connection not established');
    }
    return await this.sshService.executeCommand(command);
  }

  protected formatCodeContext(codeContext?: CodeSelection, filePath?: string): string {
    if (!codeContext || !filePath) {
      return '';
    }

    return `
File: ${filePath}
Lines ${codeContext.startLine}-${codeContext.endLine}:
\`\`\`
${codeContext.selectedText}
\`\`\`
`;
  }

  protected async checkProcessRunning(processName: string): Promise<boolean> {
    try {
      const result = await this.executeCommand(`pgrep -f "${processName}"`);
      return result.trim().length > 0;
    } catch {
      return false;
    }
  }

  protected async killProcess(processName: string): Promise<void> {
    try {
      await this.executeCommand(`pkill -f "${processName}"`);
    } catch (error) {
      console.warn(`Failed to kill process ${processName}:`, error);
    }
  }
}
