import { BaseAgent } from './BaseAgent';
import { AIAssistantConfig, AIAssistantStatus, AIMessage, CodeSelection } from '../../types';

export class ClaudeCodeAgent extends BaseAgent {
  private sessionId?: string;
  private conversationHistory: AIMessage[] = [];

  async start(config: AIAssistantConfig): Promise<void> {
    try {
      this.setStatus(AIAssistantStatus.STARTING);
      this.config = config;

      // 检查Claude Code是否已经在运行
      const isRunning = await this.checkProcessRunning('claude-code');
      if (isRunning) {
        await this.killProcess('claude-code');
        // 等待进程完全停止
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // 切换到项目目录并启动Claude Code
      const startCommand = this.buildStartCommand(config);
      console.log(`Starting Claude Code with command: ${startCommand}`);

      // 启动Claude Code进程
      const result = await this.executeCommand(startCommand);
      console.log('Claude Code start result:', result);

      // 生成会话ID
      this.sessionId = `claude_session_${Date.now()}`;

      // 等待Claude Code初始化
      await this.waitForInitialization();

      this.setStatus(AIAssistantStatus.READY);

      // 发送初始化消息
      const initMessage: AIMessage = {
        id: this.generateMessageId(),
        type: 'assistant',
        content: 'Claude Code已启动，准备协助您进行代码开发。',
        timestamp: new Date()
      };
      this.emitMessage(initMessage);

    } catch (error) {
      this.setStatus(AIAssistantStatus.ERROR);
      throw new Error(`Failed to start Claude Code: ${error}`);
    }
  }

  async stop(): Promise<void> {
    try {
      if (this.sessionId) {
        // 发送退出命令
        await this.executeCommand('echo "exit" | claude-code');
      }

      // 强制终止进程
      await this.killProcess('claude-code');

      this.sessionId = undefined;
      this.conversationHistory = [];
      this.setStatus(AIAssistantStatus.IDLE);

    } catch (error) {
      console.warn('Error stopping Claude Code:', error);
      this.setStatus(AIAssistantStatus.IDLE);
    }
  }

  async sendMessage(message: string, codeContext?: CodeSelection, filePath?: string): Promise<string> {
    if (this.status !== AIAssistantStatus.READY) {
      throw new Error('Claude Code is not ready');
    }

    try {
      this.setStatus(AIAssistantStatus.PROCESSING);

      // 创建用户消息
      const userMessage: AIMessage = {
        id: this.generateMessageId(),
        type: 'user',
        content: message,
        timestamp: new Date(),
        codeContext,
        filePath
      };

      this.conversationHistory.push(userMessage);
      this.emitMessage(userMessage);

      // 构建完整的消息内容
      const fullMessage = this.buildFullMessage(message, codeContext, filePath);

      // 发送消息到Claude Code
      const response = await this.sendToClaudeCode(fullMessage);

      // 创建助手响应消息
      const assistantMessage: AIMessage = {
        id: this.generateMessageId(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };

      this.conversationHistory.push(assistantMessage);
      this.emitMessage(assistantMessage);

      this.setStatus(AIAssistantStatus.READY);
      return response;

    } catch (error) {
      this.setStatus(AIAssistantStatus.ERROR);
      throw error;
    }
  }

  private buildStartCommand(config: AIAssistantConfig): string {
    const { projectPath, additionalArgs = [] } = config;
    
    // 构建Claude Code启动命令
    let command = `cd "${projectPath}" && claude-code`;
    
    if (additionalArgs.length > 0) {
      command += ` ${additionalArgs.join(' ')}`;
    }

    // 在后台运行
    command += ' &';
    
    return command;
  }

  private async waitForInitialization(): Promise<void> {
    // 等待Claude Code初始化完成
    const maxAttempts = 30;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        // 检查Claude Code是否响应
        const testResult = await this.executeCommand('echo "test" | timeout 5 claude-code --version 2>/dev/null || echo "not_ready"');
        
        if (!testResult.includes('not_ready')) {
          return;
        }
      } catch (error) {
        // 继续等待
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error('Claude Code initialization timeout');
  }

  private buildFullMessage(message: string, codeContext?: CodeSelection, filePath?: string): string {
    let fullMessage = message;

    // 添加代码上下文
    const contextString = this.formatCodeContext(codeContext, filePath);
    if (contextString) {
      fullMessage = `${contextString}\n\n${message}`;
    }

    return fullMessage;
  }

  private async sendToClaudeCode(message: string): Promise<string> {
    try {
      // 转义消息中的特殊字符
      const escapedMessage = message.replace(/"/g, '\\"').replace(/\n/g, '\\n');
      
      // 发送消息到Claude Code
      const command = `echo "${escapedMessage}" | claude-code`;
      const response = await this.executeCommand(command);

      return this.parseClaudeResponse(response);

    } catch (error) {
      // 如果直接发送失败，尝试通过临时文件
      return await this.sendViaTemporaryFile(message);
    }
  }

  private async sendViaTemporaryFile(message: string): Promise<string> {
    const tempFile = `/tmp/claude_input_${Date.now()}.txt`;
    
    try {
      // 写入临时文件
      await this.executeCommand(`cat > "${tempFile}" << 'EOF'\n${message}\nEOF`);
      
      // 通过临时文件发送
      const response = await this.executeCommand(`claude-code < "${tempFile}"`);
      
      // 清理临时文件
      await this.executeCommand(`rm -f "${tempFile}"`);
      
      return this.parseClaudeResponse(response);

    } catch (error) {
      // 清理临时文件
      await this.executeCommand(`rm -f "${tempFile}"`).catch(() => {});
      throw error;
    }
  }

  private parseClaudeResponse(response: string): string {
    // 解析Claude Code的响应
    // 移除可能的控制字符和格式化
    let cleanResponse = response
      .replace(/\x1b\[[0-9;]*m/g, '') // 移除ANSI颜色代码
      .replace(/^\s+|\s+$/g, '') // 移除首尾空白
      .trim();

    if (!cleanResponse) {
      cleanResponse = '收到您的消息，但Claude Code没有返回响应。';
    }

    return cleanResponse;
  }

  getConversationHistory(): AIMessage[] {
    return [...this.conversationHistory];
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }
}
