import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AppState,
  SSHConfig,
  ConnectionStatus,
  AIAssistantStatus,
  FileSystemItem,
  CodeSelection,
  AIMessage,
} from '../types';

const STORAGE_KEYS = {
  SSH_CONFIG: 'ssh_config',
  RECENT_CONNECTIONS: 'recent_connections',
  AI_MESSAGES: 'ai_messages',
};

export const useAppState = () => {
  const [appState, setAppState] = useState<AppState>({
    sshConnection: {
      status: ConnectionStatus.DISCONNECTED,
    },
    fileSystem: {
      currentPath: '/home',
      items: [],
      loading: false,
    },
    codeEditor: {
      content: '',
    },
    aiAssistant: {
      status: AIAssistantStatus.IDLE,
      messages: [],
    },
  });

  // 加载保存的SSH配置
  const loadSavedSSHConfig = async (): Promise<SSHConfig | null> => {
    try {
      const savedConfig = await AsyncStorage.getItem(STORAGE_KEYS.SSH_CONFIG);
      return savedConfig ? JSON.parse(savedConfig) : null;
    } catch (error) {
      console.warn('Failed to load SSH config:', error);
      return null;
    }
  };

  // 保存SSH配置
  const saveSSHConfig = async (config: SSHConfig): Promise<void> => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.SSH_CONFIG, JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save SSH config:', error);
    }
  };

  // 加载最近的连接记录
  const loadRecentConnections = async (): Promise<SSHConfig[]> => {
    try {
      const recentConnections = await AsyncStorage.getItem(STORAGE_KEYS.RECENT_CONNECTIONS);
      return recentConnections ? JSON.parse(recentConnections) : [];
    } catch (error) {
      console.warn('Failed to load recent connections:', error);
      return [];
    }
  };

  // 保存最近的连接记录
  const saveRecentConnection = async (config: SSHConfig): Promise<void> => {
    try {
      const recentConnections = await loadRecentConnections();
      const filteredConnections = recentConnections.filter(
        (conn) => conn.host !== config.host || conn.username !== config.username
      );
      const updatedConnections = [config, ...filteredConnections].slice(0, 5); // 保留最近5个
      await AsyncStorage.setItem(STORAGE_KEYS.RECENT_CONNECTIONS, JSON.stringify(updatedConnections));
    } catch (error) {
      console.warn('Failed to save recent connection:', error);
    }
  };

  // 加载AI消息历史
  const loadAIMessages = async (): Promise<AIMessage[]> => {
    try {
      const messages = await AsyncStorage.getItem(STORAGE_KEYS.AI_MESSAGES);
      return messages ? JSON.parse(messages) : [];
    } catch (error) {
      console.warn('Failed to load AI messages:', error);
      return [];
    }
  };

  // 保存AI消息历史
  const saveAIMessages = async (messages: AIMessage[]): Promise<void> => {
    try {
      // 只保存最近100条消息
      const recentMessages = messages.slice(-100);
      await AsyncStorage.setItem(STORAGE_KEYS.AI_MESSAGES, JSON.stringify(recentMessages));
    } catch (error) {
      console.warn('Failed to save AI messages:', error);
    }
  };

  // 更新SSH连接状态
  const updateSSHConnection = (updates: Partial<AppState['sshConnection']>) => {
    setAppState(prev => ({
      ...prev,
      sshConnection: {
        ...prev.sshConnection,
        ...updates,
      },
    }));
  };

  // 更新文件系统状态
  const updateFileSystem = (updates: Partial<AppState['fileSystem']>) => {
    setAppState(prev => ({
      ...prev,
      fileSystem: {
        ...prev.fileSystem,
        ...updates,
      },
    }));
  };

  // 更新代码编辑器状态
  const updateCodeEditor = (updates: Partial<AppState['codeEditor']>) => {
    setAppState(prev => ({
      ...prev,
      codeEditor: {
        ...prev.codeEditor,
        ...updates,
      },
    }));
  };

  // 更新AI助手状态
  const updateAIAssistant = (updates: Partial<AppState['aiAssistant']>) => {
    setAppState(prev => ({
      ...prev,
      aiAssistant: {
        ...prev.aiAssistant,
        ...updates,
      },
    }));

    // 自动保存消息历史
    if (updates.messages) {
      saveAIMessages(updates.messages);
    }
  };

  // 添加AI消息
  const addAIMessage = (message: AIMessage) => {
    setAppState(prev => {
      const newMessages = [...prev.aiAssistant.messages, message];
      // 自动保存消息历史
      saveAIMessages(newMessages);
      return {
        ...prev,
        aiAssistant: {
          ...prev.aiAssistant,
          messages: newMessages,
        },
      };
    });
  };

  // 清除AI消息历史
  const clearAIMessages = async () => {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AI_MESSAGES);
      updateAIAssistant({ messages: [] });
    } catch (error) {
      console.warn('Failed to clear AI messages:', error);
    }
  };

  // 重置应用状态
  const resetAppState = () => {
    setAppState({
      sshConnection: {
        status: ConnectionStatus.DISCONNECTED,
      },
      fileSystem: {
        currentPath: '/home',
        items: [],
        loading: false,
      },
      codeEditor: {
        content: '',
      },
      aiAssistant: {
        status: AIAssistantStatus.IDLE,
        messages: [],
      },
    });
  };

  // 初始化时加载保存的数据
  useEffect(() => {
    const initializeAppState = async () => {
      const savedMessages = await loadAIMessages();
      if (savedMessages.length > 0) {
        updateAIAssistant({ messages: savedMessages });
      }
    };

    initializeAppState();
  }, []);

  return {
    appState,
    updateSSHConnection,
    updateFileSystem,
    updateCodeEditor,
    updateAIAssistant,
    addAIMessage,
    clearAIMessages,
    resetAppState,
    loadSavedSSHConfig,
    saveSSHConfig,
    loadRecentConnections,
    saveRecentConnection,
  };
};
