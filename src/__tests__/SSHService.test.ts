import { SSHService } from '../services/SSHService';
import { SSHConfig, ConnectionStatus } from '../types';

describe('SSHService', () => {
  let sshService: SSHService;

  beforeEach(() => {
    sshService = new SSHService();
  });

  afterEach(async () => {
    if (sshService.isConnected()) {
      await sshService.disconnect();
    }
  });

  test('should initialize with disconnected status', () => {
    expect(sshService.getStatus()).toBe(ConnectionStatus.DISCONNECTED);
    expect(sshService.isConnected()).toBe(false);
  });

  test('should handle connection attempt', async () => {
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    // 由于这是模拟实现，连接应该成功
    await expect(sshService.connect(config)).resolves.not.toThrow();
    expect(sshService.getStatus()).toBe(ConnectionStatus.CONNECTED);
    expect(sshService.isConnected()).toBe(true);
  });

  test('should handle disconnection', async () => {
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    await sshService.connect(config);
    await sshService.disconnect();
    
    expect(sshService.getStatus()).toBe(ConnectionStatus.DISCONNECTED);
    expect(sshService.isConnected()).toBe(false);
  });

  test('should execute commands when connected', async () => {
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    await sshService.connect(config);
    
    const result = await sshService.executeCommand('ls -la');
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
  });

  test('should throw error when executing commands without connection', async () => {
    await expect(sshService.executeCommand('ls -la')).rejects.toThrow('SSH connection not established');
  });

  test('should list directory contents', async () => {
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    await sshService.connect(config);
    
    const items = await sshService.listDirectory('/home');
    expect(Array.isArray(items)).toBe(true);
    expect(items.length).toBeGreaterThan(0);
    
    // 检查返回的项目结构
    items.forEach(item => {
      expect(item).toHaveProperty('name');
      expect(item).toHaveProperty('path');
      expect(item).toHaveProperty('type');
      expect(['file', 'directory']).toContain(item.type);
    });
  });

  test('should read file contents', async () => {
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    await sshService.connect(config);
    
    const content = await sshService.readFile('/home/<USER>');
    expect(typeof content).toBe('string');
    expect(content.length).toBeGreaterThan(0);
  });

  test('should handle status change callbacks', (done) => {
    let callbackCount = 0;
    
    sshService.onStatusChange((status) => {
      callbackCount++;
      if (callbackCount === 1) {
        expect(status).toBe(ConnectionStatus.CONNECTING);
      } else if (callbackCount === 2) {
        expect(status).toBe(ConnectionStatus.CONNECTED);
        done();
      }
    });

    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };

    sshService.connect(config);
  });
});
