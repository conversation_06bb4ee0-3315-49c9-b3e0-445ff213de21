import { ClaudeCodeAgent } from '../services/agents/ClaudeCodeAgent';
import { SSHService } from '../services/SSHService';
import { AIAssistantConfig, AIAssistantType, AIAssistantStatus, SSHConfig, ConnectionStatus } from '../types';

describe('ClaudeCodeAgent', () => {
  let sshService: SSHService;
  let agent: ClaudeCodeAgent;

  beforeEach(async () => {
    sshService = new SSHService();
    agent = new ClaudeCodeAgent(sshService);

    // 建立SSH连接
    const config: SSHConfig = {
      host: 'test.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
    };
    await sshService.connect(config);
  });

  afterEach(async () => {
    if (agent.getStatus() !== AIAssistantStatus.IDLE) {
      await agent.stop();
    }
    if (sshService.isConnected()) {
      await sshService.disconnect();
    }
  });

  test('should initialize with idle status', () => {
    expect(agent.getStatus()).toBe(AIAssistantStatus.IDLE);
  });

  test('should start Claude Code agent', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await expect(agent.start(config)).resolves.not.toThrow();
    expect(agent.getStatus()).toBe(AIAssistantStatus.READY);
  });

  test('should stop Claude Code agent', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await agent.start(config);
    await agent.stop();
    
    expect(agent.getStatus()).toBe(AIAssistantStatus.IDLE);
  });

  test('should send message to Claude Code', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await agent.start(config);
    
    const response = await agent.sendMessage('Hello, Claude!');
    expect(typeof response).toBe('string');
    expect(response.length).toBeGreaterThan(0);
  });

  test('should handle message with code context', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await agent.start(config);
    
    const codeContext = {
      startLine: 1,
      endLine: 5,
      startColumn: 0,
      endColumn: 0,
      selectedText: 'function hello() {\n  console.log("Hello");\n}',
    };

    const response = await agent.sendMessage(
      'Please review this function',
      codeContext,
      '/home/<USER>/main.js'
    );
    
    expect(typeof response).toBe('string');
    expect(response.length).toBeGreaterThan(0);
  });

  test('should handle status change callbacks', (done) => {
    let callbackCount = 0;
    
    agent.onStatusChange((status) => {
      callbackCount++;
      if (callbackCount === 1) {
        expect(status).toBe(AIAssistantStatus.STARTING);
      } else if (callbackCount === 2) {
        expect(status).toBe(AIAssistantStatus.READY);
        done();
      }
    });

    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    agent.start(config);
  });

  test('should handle message callbacks', (done) => {
    agent.onMessage((message) => {
      expect(message).toHaveProperty('id');
      expect(message).toHaveProperty('type');
      expect(message).toHaveProperty('content');
      expect(message).toHaveProperty('timestamp');
      expect(['user', 'assistant']).toContain(message.type);
      done();
    });

    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    agent.start(config).then(() => {
      agent.sendMessage('Test message');
    });
  });

  test('should throw error when sending message without starting', async () => {
    await expect(agent.sendMessage('Hello')).rejects.toThrow('Claude Code is not ready');
  });

  test('should handle conversation history', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await agent.start(config);
    
    // 发送几条消息
    await agent.sendMessage('First message');
    await agent.sendMessage('Second message');
    
    const history = (agent as any).getConversationHistory();
    expect(Array.isArray(history)).toBe(true);
    expect(history.length).toBeGreaterThan(0);
    
    // 检查消息结构
    history.forEach((message: any) => {
      expect(message).toHaveProperty('id');
      expect(message).toHaveProperty('type');
      expect(message).toHaveProperty('content');
      expect(message).toHaveProperty('timestamp');
    });
  });

  test('should clear conversation history', async () => {
    const config: AIAssistantConfig = {
      type: AIAssistantType.CLAUDE_CODE,
      projectPath: '/home/<USER>',
    };

    await agent.start(config);
    await agent.sendMessage('Test message');
    
    (agent as any).clearHistory();
    const history = (agent as any).getConversationHistory();
    expect(history.length).toBe(0);
  });
});
