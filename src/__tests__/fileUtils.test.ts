import {
  getFileExtension,
  getLanguageFromExtension,
  getFileIcon,
  formatFileSize,
  isTextFile,
  isImageFile,
  isCodeFile,
  getFileCategory,
} from '../utils/fileUtils';

describe('fileUtils', () => {
  describe('getFileExtension', () => {
    test('should extract file extension correctly', () => {
      expect(getFileExtension('test.js')).toBe('js');
      expect(getFileExtension('package.json')).toBe('json');
      expect(getFileExtension('README.md')).toBe('md');
      expect(getFileExtension('script.sh')).toBe('sh');
      expect(getFileExtension('style.css')).toBe('css');
    });

    test('should handle files without extension', () => {
      expect(getFileExtension('README')).toBe('');
      expect(getFileExtension('Makefile')).toBe('');
      expect(getFileExtension('dockerfile')).toBe('');
    });

    test('should handle multiple dots in filename', () => {
      expect(getFileExtension('test.min.js')).toBe('js');
      expect(getFileExtension('config.dev.json')).toBe('json');
    });

    test('should be case insensitive', () => {
      expect(getFileExtension('Test.JS')).toBe('js');
      expect(getFileExtension('README.MD')).toBe('md');
    });
  });

  describe('getLanguageFromExtension', () => {
    test('should return correct language for common extensions', () => {
      expect(getLanguageFromExtension('test.js')).toBe('javascript');
      expect(getLanguageFromExtension('test.jsx')).toBe('javascript');
      expect(getLanguageFromExtension('test.ts')).toBe('typescript');
      expect(getLanguageFromExtension('test.tsx')).toBe('typescript');
      expect(getLanguageFromExtension('test.py')).toBe('python');
      expect(getLanguageFromExtension('test.java')).toBe('java');
      expect(getLanguageFromExtension('test.cpp')).toBe('cpp');
      expect(getLanguageFromExtension('test.c')).toBe('c');
      expect(getLanguageFromExtension('test.html')).toBe('html');
      expect(getLanguageFromExtension('test.css')).toBe('css');
      expect(getLanguageFromExtension('test.json')).toBe('json');
      expect(getLanguageFromExtension('test.md')).toBe('markdown');
    });

    test('should return text for unknown extensions', () => {
      expect(getLanguageFromExtension('test.unknown')).toBe('text');
      expect(getLanguageFromExtension('test')).toBe('text');
    });
  });

  describe('getFileIcon', () => {
    test('should return folder icon for directories', () => {
      expect(getFileIcon('src', true)).toBe('📁');
      expect(getFileIcon('node_modules', true)).toBe('📁');
    });

    test('should return appropriate icons for different file types', () => {
      expect(getFileIcon('test.js')).toBe('🟨');
      expect(getFileIcon('test.jsx')).toBe('⚛️');
      expect(getFileIcon('test.ts')).toBe('🔷');
      expect(getFileIcon('test.tsx')).toBe('⚛️');
      expect(getFileIcon('test.py')).toBe('🐍');
      expect(getFileIcon('test.java')).toBe('☕');
      expect(getFileIcon('test.html')).toBe('🌐');
      expect(getFileIcon('test.css')).toBe('🎨');
      expect(getFileIcon('test.json')).toBe('📋');
      expect(getFileIcon('test.md')).toBe('📝');
      expect(getFileIcon('test.png')).toBe('🖼️');
      expect(getFileIcon('test.jpg')).toBe('🖼️');
    });

    test('should handle special filenames', () => {
      expect(getFileIcon('README.md')).toBe('📖');
      expect(getFileIcon('readme.txt')).toBe('📖');
      expect(getFileIcon('LICENSE')).toBe('📜');
      expect(getFileIcon('Dockerfile')).toBe('🐳');
      expect(getFileIcon('Makefile')).toBe('🔨');
      expect(getFileIcon('.gitignore')).toBe('🚫');
      expect(getFileIcon('package.json')).toBe('📦');
    });

    test('should return default icon for unknown files', () => {
      expect(getFileIcon('unknown.xyz')).toBe('📄');
      expect(getFileIcon('test')).toBe('📄');
    });
  });

  describe('formatFileSize', () => {
    test('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('');
      expect(formatFileSize(undefined)).toBe('');
      expect(formatFileSize(512)).toBe('512B');
      expect(formatFileSize(1023)).toBe('1023B');
    });

    test('should format kilobytes correctly', () => {
      expect(formatFileSize(1024)).toBe('1.0KB');
      expect(formatFileSize(1536)).toBe('1.5KB');
      expect(formatFileSize(1024 * 1023)).toBe('1023.0KB');
    });

    test('should format megabytes correctly', () => {
      expect(formatFileSize(1024 * 1024)).toBe('1.0MB');
      expect(formatFileSize(1024 * 1024 * 1.5)).toBe('1.5MB');
      expect(formatFileSize(1024 * 1024 * 1023)).toBe('1023.0MB');
    });

    test('should format gigabytes correctly', () => {
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1.0GB');
      expect(formatFileSize(1024 * 1024 * 1024 * 2.5)).toBe('2.5GB');
    });
  });

  describe('isTextFile', () => {
    test('should identify text files correctly', () => {
      expect(isTextFile('test.txt')).toBe(true);
      expect(isTextFile('test.md')).toBe(true);
      expect(isTextFile('test.json')).toBe(true);
      expect(isTextFile('test.js')).toBe(true);
      expect(isTextFile('test.py')).toBe(true);
      expect(isTextFile('test.html')).toBe(true);
      expect(isTextFile('test.css')).toBe(true);
      expect(isTextFile('README')).toBe(true);
      expect(isTextFile('readme.txt')).toBe(true);
    });

    test('should identify non-text files correctly', () => {
      expect(isTextFile('test.png')).toBe(false);
      expect(isTextFile('test.jpg')).toBe(false);
      expect(isTextFile('test.pdf')).toBe(false);
      expect(isTextFile('test.zip')).toBe(false);
      expect(isTextFile('test.exe')).toBe(false);
    });
  });

  describe('isImageFile', () => {
    test('should identify image files correctly', () => {
      expect(isImageFile('test.png')).toBe(true);
      expect(isImageFile('test.jpg')).toBe(true);
      expect(isImageFile('test.jpeg')).toBe(true);
      expect(isImageFile('test.gif')).toBe(true);
      expect(isImageFile('test.svg')).toBe(true);
      expect(isImageFile('test.ico')).toBe(true);
      expect(isImageFile('test.bmp')).toBe(true);
      expect(isImageFile('test.webp')).toBe(true);
    });

    test('should identify non-image files correctly', () => {
      expect(isImageFile('test.txt')).toBe(false);
      expect(isImageFile('test.js')).toBe(false);
      expect(isImageFile('test.pdf')).toBe(false);
      expect(isImageFile('test.zip')).toBe(false);
    });
  });

  describe('isCodeFile', () => {
    test('should identify code files correctly', () => {
      expect(isCodeFile('test.js')).toBe(true);
      expect(isCodeFile('test.jsx')).toBe(true);
      expect(isCodeFile('test.ts')).toBe(true);
      expect(isCodeFile('test.tsx')).toBe(true);
      expect(isCodeFile('test.py')).toBe(true);
      expect(isCodeFile('test.java')).toBe(true);
      expect(isCodeFile('test.cpp')).toBe(true);
      expect(isCodeFile('test.c')).toBe(true);
      expect(isCodeFile('test.html')).toBe(true);
      expect(isCodeFile('test.css')).toBe(true);
      expect(isCodeFile('test.sh')).toBe(true);
    });

    test('should identify non-code files correctly', () => {
      expect(isCodeFile('test.txt')).toBe(false);
      expect(isCodeFile('test.md')).toBe(false);
      expect(isCodeFile('test.json')).toBe(false);
      expect(isCodeFile('test.png')).toBe(false);
      expect(isCodeFile('test.pdf')).toBe(false);
    });
  });

  describe('getFileCategory', () => {
    test('should categorize files correctly', () => {
      expect(getFileCategory('test.js')).toBe('code');
      expect(getFileCategory('test.py')).toBe('code');
      expect(getFileCategory('test.html')).toBe('code');
      
      expect(getFileCategory('test.json')).toBe('config');
      expect(getFileCategory('test.xml')).toBe('config');
      expect(getFileCategory('test.yml')).toBe('config');
      
      expect(getFileCategory('test.md')).toBe('document');
      expect(getFileCategory('test.txt')).toBe('document');
      expect(getFileCategory('test.pdf')).toBe('document');
      
      expect(getFileCategory('test.png')).toBe('image');
      expect(getFileCategory('test.jpg')).toBe('image');
      
      expect(getFileCategory('test.zip')).toBe('other');
      expect(getFileCategory('test.exe')).toBe('other');
    });
  });
});
