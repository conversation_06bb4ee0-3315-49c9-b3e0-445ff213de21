import { Platform } from 'react-native';
import { DeviceInfo } from './DeviceInfo';

export interface PlatformInfo {
  isIOS: boolean;
  isAndroid: boolean;
  isIOSSimulator: boolean;
  isIOSDevice: boolean;
  supportsSSH: boolean;
}

export class PlatformDetector {
  private static instance: PlatformDetector;
  private platformInfo: PlatformInfo;

  private constructor() {
    this.platformInfo = this.detectPlatform();
  }

  public static getInstance(): PlatformDetector {
    if (!PlatformDetector.instance) {
      PlatformDetector.instance = new PlatformDetector();
    }
    return PlatformDetector.instance;
  }

  public getPlatformInfo(): PlatformInfo {
    return this.platformInfo;
  }

  public isSSHSupported(): boolean {
    return this.platformInfo.supportsSSH;
  }



  private detectPlatform(): PlatformInfo {
    const isIOS = Platform.OS === 'ios';
    const isAndroid = Platform.OS === 'android';

    // 使用更精确的设备检测
    const isIOSSimulator = DeviceInfo.isIOSSimulator();
    const isIOSDevice = isIOS && !isIOSSimulator;

    // SSH支持情况：
    // - Android: 完全支持
    // - iOS真机: 支持
    // - iOS模拟器: 不支持（需要使用Mock）
    const supportsSSH = DeviceInfo.supportsNativeSSH();

    return {
      isIOS,
      isAndroid,
      isIOSSimulator,
      isIOSDevice,
      supportsSSH
    };
  }

  public logPlatformInfo(): void {
    console.log('Platform Detection Results:', {
      ...DeviceInfo.getDetailedInfo(),
      ...this.platformInfo
    });
  }
}

export default PlatformDetector;
