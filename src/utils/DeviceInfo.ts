import { Platform, Dimensions } from 'react-native';

/**
 * 更精确的设备信息检测工具
 * 用于区分iOS模拟器和真机
 */
export class DeviceInfo {
  /**
   * 检测是否为iOS模拟器
   * 使用多种方法来提高检测准确性
   */
  public static isIOSSimulator(): boolean {
    if (Platform.OS !== 'ios') {
      return false;
    }

    // 方法1: 检查开发环境
    if (!__DEV__) {
      // 生产环境通常不在模拟器上运行
      return false;
    }

    // 方法2: 检查屏幕尺寸特征
    // 模拟器通常有特定的屏幕尺寸
    const { width, height } = Dimensions.get('window');
    const screenRatio = Math.max(width, height) / Math.min(width, height);
    
    // 常见的iOS模拟器屏幕比例
    const commonSimulatorRatios = [
      2.16, // iPhone X系列
      1.78, // iPhone 6/7/8系列
      1.5,  // iPhone 5系列
      1.33, // iPad系列
    ];

    const isCommonRatio = commonSimulatorRatios.some(ratio => 
      Math.abs(screenRatio - ratio) < 0.1
    );

    // 方法3: 检查特定的模拟器特征
    // 在模拟器中，某些原生功能可能不可用或表现不同
    try {
      // 这里可以添加更多的模拟器特征检测
      // 例如检查某些原生模块的可用性
      
      // 简化的检测：在开发环境下，如果是常见的屏幕比例，很可能是模拟器
      return isCommonRatio;
    } catch {
      // 如果检测过程中出错，保守地认为是真机
      return false;
    }
  }

  /**
   * 获取设备类型描述
   */
  public static getDeviceType(): string {
    if (Platform.OS === 'android') {
      return 'Android Device';
    } else if (Platform.OS === 'ios') {
      return this.isIOSSimulator() ? 'iOS Simulator' : 'iOS Device';
    } else {
      return 'Unknown Platform';
    }
  }

  /**
   * 检查是否支持原生SSH功能
   */
  public static supportsNativeSSH(): boolean {
    // Android设备支持
    if (Platform.OS === 'android') {
      return true;
    }
    
    // iOS真机支持，模拟器不支持
    if (Platform.OS === 'ios') {
      return !this.isIOSSimulator();
    }
    
    return false;
  }

  /**
   * 获取详细的设备信息
   */
  public static getDetailedInfo() {
    const { width, height } = Dimensions.get('window');
    const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');
    
    return {
      platform: Platform.OS,
      version: Platform.Version,
      isTV: Platform.isTV,
      windowSize: { width, height },
      screenSize: { width: screenWidth, height: screenHeight },
      deviceType: this.getDeviceType(),
      isSimulator: this.isIOSSimulator(),
      supportsSSH: this.supportsNativeSSH(),
      isDev: __DEV__,
    };
  }
}
