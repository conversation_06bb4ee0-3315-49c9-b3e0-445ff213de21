// 文件类型工具函数

export const getFileExtension = (filename: string): string => {
  const parts = filename.split('.');
  if (parts.length <= 1) {
    return '';
  }
  return parts.pop()?.toLowerCase() || '';
};

export const getLanguageFromExtension = (filename: string): string => {
  const extension = getFileExtension(filename);
  const languageMap: { [key: string]: string } = {
    js: 'javascript',
    jsx: 'javascript',
    ts: 'typescript',
    tsx: 'typescript',
    py: 'python',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    h: 'c',
    hpp: 'cpp',
    html: 'html',
    htm: 'html',
    css: 'css',
    scss: 'scss',
    sass: 'sass',
    less: 'less',
    json: 'json',
    xml: 'xml',
    md: 'markdown',
    markdown: 'markdown',
    sql: 'sql',
    sh: 'bash',
    bash: 'bash',
    zsh: 'bash',
    yml: 'yaml',
    yaml: 'yaml',
    php: 'php',
    rb: 'ruby',
    go: 'go',
    rs: 'rust',
    swift: 'swift',
    kt: 'kotlin',
    scala: 'scala',
    r: 'r',
    m: 'objective-c',
    mm: 'objective-c',
    pl: 'perl',
    lua: 'lua',
    vim: 'vim',
    dockerfile: 'dockerfile',
    makefile: 'makefile',
    cmake: 'cmake',
    gradle: 'gradle',
    properties: 'properties',
    ini: 'ini',
    conf: 'conf',
    config: 'conf',
    log: 'log',
    txt: 'text',
  };
  return languageMap[extension] || 'text';
};

export const getFileIcon = (filename: string, isDirectory: boolean = false): string => {
  if (isDirectory) {
    return '[DIR]';
  }

  const extension = getFileExtension(filename);
  const iconMap: { [key: string]: string } = {
    // 编程语言
    js: '[JS]',
    jsx: '[JSX]',
    ts: '[TS]',
    tsx: '[TSX]',
    py: '[PY]',
    java: '[JAVA]',
    cpp: '[CPP]',
    c: '[C]',
    h: '[H]',
    hpp: '[HPP]',
    go: '[GO]',
    rs: '[RS]',
    swift: '[SWIFT]',
    kt: '[KT]',
    scala: '[SCALA]',
    rb: '[RB]',
    php: '[PHP]',

    // Web技术
    html: '[HTML]',
    htm: '[HTM]',
    css: '[CSS]',
    scss: '[SCSS]',
    sass: '[SASS]',
    less: '[LESS]',

    // 数据格式
    json: '[JSON]',
    xml: '[XML]',
    yml: '[YML]',
    yaml: '[YAML]',
    csv: '[CSV]',

    // 文档
    md: '[MD]',
    markdown: '[MD]',
    txt: '[TXT]',
    pdf: '[PDF]',
    doc: '[DOC]',
    docx: '[DOCX]',

    // 配置文件
    conf: '[CONF]',
    config: '[CONFIG]',
    ini: '[INI]',
    properties: '[PROP]',

    // 脚本
    sh: '[SH]',
    bash: '[BASH]',
    zsh: '[ZSH]',
    bat: '[BAT]',
    cmd: '[CMD]',

    // 数据库
    sql: '[SQL]',
    db: '[DB]',
    sqlite: '[SQLITE]',

    // 图片
    png: '[IMG]',
    jpg: '[IMG]',
    jpeg: '[IMG]',
    gif: '[IMG]',
    svg: '[SVG]',
    ico: '[ICO]',

    // 音频
    mp3: '[AUDIO]',
    wav: '[AUDIO]',
    flac: '[AUDIO]',

    // 视频
    mp4: '[VIDEO]',
    avi: '[VIDEO]',
    mov: '[VIDEO]',

    // 压缩文件
    zip: '[ZIP]',
    rar: '[RAR]',
    tar: '[TAR]',
    gz: '[GZ]',

    // 其他
    log: '[LOG]',
    lock: '[LOCK]',
    gitignore: '[GIT]',
    dockerfile: '[DOCKER]',
    makefile: '[MAKE]',
    readme: '[README]',
  };
  
  // 特殊文件名处理
  const lowerName = filename.toLowerCase();
  if (lowerName.includes('readme')) return '[README]';
  if (lowerName.includes('license')) return '[LICENSE]';
  if (lowerName.includes('changelog')) return '[CHANGELOG]';
  if (lowerName.includes('dockerfile')) return '[DOCKER]';
  if (lowerName.includes('makefile')) return '[MAKE]';
  if (lowerName.includes('.gitignore')) return '[GIT]';
  if (lowerName.includes('package.json')) return '[PKG]';
  if (lowerName.includes('tsconfig')) return '[TSCONFIG]';
  if (lowerName.includes('webpack')) return '[WEBPACK]';
  if (lowerName.includes('babel')) return '[BABEL]';
  if (lowerName.includes('eslint')) return '[ESLINT]';
  if (lowerName.includes('prettier')) return '[PRETTIER]';

  return iconMap[extension] || '[FILE]';
};

export const formatFileSize = (size?: number): string => {
  if (!size) return '';
  
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)}MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)}GB`;
};

export const isTextFile = (filename: string): boolean => {
  const extension = getFileExtension(filename);
  const textExtensions = [
    'txt', 'md', 'markdown', 'json', 'xml', 'html', 'htm', 'css', 'scss', 'sass', 'less',
    'js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'hpp', 'go', 'rs', 'swift',
    'kt', 'scala', 'rb', 'php', 'sql', 'sh', 'bash', 'zsh', 'yml', 'yaml', 'conf', 'config',
    'ini', 'properties', 'log', 'dockerfile', 'makefile', 'gradle', 'r', 'm', 'mm', 'pl',
    'lua', 'vim', 'gitignore', 'license', 'changelog', 'readme'
  ];
  
  return textExtensions.includes(extension) || filename.toLowerCase().includes('readme');
};

export const isImageFile = (filename: string): boolean => {
  const extension = getFileExtension(filename);
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'bmp', 'webp'];
  return imageExtensions.includes(extension);
};

export const isCodeFile = (filename: string): boolean => {
  const extension = getFileExtension(filename);
  const codeExtensions = [
    'js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'hpp', 'go', 'rs', 'swift',
    'kt', 'scala', 'rb', 'php', 'sql', 'sh', 'bash', 'zsh', 'html', 'htm', 'css', 'scss',
    'sass', 'less', 'r', 'm', 'mm', 'pl', 'lua', 'vim'
  ];
  return codeExtensions.includes(extension);
};

export const getFileCategory = (filename: string): 'code' | 'config' | 'document' | 'image' | 'other' => {
  if (isCodeFile(filename)) return 'code';
  if (isImageFile(filename)) return 'image';
  
  const extension = getFileExtension(filename);
  const configExtensions = ['json', 'xml', 'yml', 'yaml', 'conf', 'config', 'ini', 'properties'];
  if (configExtensions.includes(extension)) return 'config';
  
  const documentExtensions = ['md', 'markdown', 'txt', 'pdf', 'doc', 'docx'];
  if (documentExtensions.includes(extension)) return 'document';
  
  return 'other';
};
