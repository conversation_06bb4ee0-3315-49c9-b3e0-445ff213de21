declare module '@dylankenneally/react-native-ssh-sftp' {
  export interface SSHClient {
    execute(command: string): Promise<string>;
    disconnect(): Promise<void>;
    startShell(ptyType?: string): Promise<void>;
    writeToShell(command: string): Promise<void>;
    closeShell(): void;
    connectSFTP(): Promise<void>;
    sftpLs(path: string): Promise<any[]>;
    sftpMkdir(dirName: string): Promise<void>;
    sftpRename(oldName: string, newName: string): Promise<void>;
    sftpRmdir(dirName: string): Promise<void>;
    sftpRm(fileName: string): Promise<void>;
    sftpDownload(remotePath: string, localPath: string): Promise<string>;
    sftpUpload(localPath: string, remotePath: string): Promise<void>;
    sftpCancelDownload(): void;
    sftpCancelUpload(): void;
    disconnectSFTP(): void;
    on(event: string, callback: (data: any) => void): void;
  }

  export default class SSHClient {
    static connectWithPassword(
      host: string,
      port: number,
      username: string,
      password: string
    ): Promise<SSHClient>;

    static connectWithKey(
      host: string,
      port: number,
      username: string,
      privateKey: string,
      passphrase?: string
    ): Promise<SSHClient>;

    execute(command: string): Promise<string>;
    disconnect(): Promise<void>;
    startShell(ptyType?: string): Promise<void>;
    writeToShell(command: string): Promise<void>;
    closeShell(): void;
    connectSFTP(): Promise<void>;
    sftpLs(path: string): Promise<any[]>;
    sftpMkdir(dirName: string): Promise<void>;
    sftpRename(oldName: string, newName: string): Promise<void>;
    sftpRmdir(dirName: string): Promise<void>;
    sftpRm(fileName: string): Promise<void>;
    sftpDownload(remotePath: string, localPath: string): Promise<string>;
    sftpUpload(localPath: string, remotePath: string): Promise<void>;
    sftpCancelDownload(): void;
    sftpCancelUpload(): void;
    disconnectSFTP(): void;
    on(event: string, callback: (data: any) => void): void;
  }
}