import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Modal,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Text as UIText } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import {
  SSHConfig,
  ConnectionStatus,
  FileSystemItem,
  CodeSelection,
  AIAssistantType,
  AIAssistantConfig,
  AIAssistantStatus,
  AIAgent,
} from '../types';
import { SSHService } from '../services/SSHService';
import { AgentFactory } from '../services/agents/AgentFactory';
import { FileSystemBrowser } from '../components/FileSystemBrowser';
import { CodeViewer } from '../components/CodeViewer';
import { AIAssistantChat } from '../components/AIAssistantChat';
import { SSHConnectionModal } from '../components/SSHConnectionModal';

export const MainScreen: React.FC = () => {
  // SSH连接状态
  const [sshService] = useState(() => new SSHService());
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(
    ConnectionStatus.DISCONNECTED
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // 文件系统状态
  const [currentPath, setCurrentPath] = useState('/home');
  const [selectedFile, setSelectedFile] = useState<FileSystemItem | null>(null);
  const [showCodeViewer, setShowCodeViewer] = useState(false);

  // AI助手状态
  const [aiAgent, setAiAgent] = useState<AIAgent | null>(null);
  const [aiStatus, setAiStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);

  // 代码选择状态
  const [codeSelection, setCodeSelection] = useState<CodeSelection | null>(null);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);

  // 界面状态
  const [activeTab, setActiveTab] = useState<'files' | 'chat'>('files');

  useEffect(() => {
    // 监听SSH连接状态变化
    sshService.onStatusChange((status) => {
      setConnectionStatus(status);
      if (status === ConnectionStatus.DISCONNECTED && aiAgent) {
        // SSH断开时停止AI助手
        aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
    });
  }, [sshService, aiAgent]);

  const handleConnect = async (config: SSHConfig) => {
    try {
      await sshService.connect(config);
      setShowConnectionModal(false);
      Alert.alert('成功', 'SSH连接已建立');
    } catch (error) {
      Alert.alert('连接失败', `无法连接到服务器: ${error}`);
    }
  };

  const handleDisconnect = async () => {
    try {
      if (aiAgent) {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
      await sshService.disconnect();
      Alert.alert('已断开', 'SSH连接已断开');
    } catch (error) {
      Alert.alert('错误', `断开连接时出错: ${error}`);
    }
  };

  const handleFileSelect = (file: FileSystemItem) => {
    setSelectedFile(file);
    setShowCodeViewer(true);
  };

  const handleCodeSelect = (selection: CodeSelection) => {
    setCodeSelection(selection);
    setSelectedFilePath(selectedFile?.path || null);
    setShowCodeViewer(false);
    setActiveTab('chat');
  };

  const handleClearCodeContext = () => {
    setCodeSelection(null);
    setSelectedFilePath(null);
  };

  const startAIAssistant = async () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      Alert.alert('错误', '请先建立SSH连接');
      return;
    }

    try {
      const agent = AgentFactory.createAgent(AIAssistantType.CLAUDE_CODE, sshService);
      
      agent.onStatusChange((status) => {
        setAiStatus(status);
      });

      const config: AIAssistantConfig = {
        type: AIAssistantType.CLAUDE_CODE,
        projectPath: currentPath,
      };

      await agent.start(config);
      setAiAgent(agent);
      setActiveTab('chat');
      
      Alert.alert('成功', 'Claude Code已启动');
    } catch (error) {
      Alert.alert('启动失败', `无法启动AI助手: ${error}`);
    }
  };

  const stopAIAssistant = async () => {
    if (aiAgent) {
      try {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
        Alert.alert('已停止', 'AI助手已停止');
      } catch (error) {
        Alert.alert('错误', `停止AI助手时出错: ${error}`);
      }
    }
  };

  const renderConnectionStatus = () => {
    const getStatusColor = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '#4CAF50';
        case ConnectionStatus.CONNECTING:
          return '#FF9800';
        case ConnectionStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getStatusText = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '已连接';
        case ConnectionStatus.CONNECTING:
          return '连接中...';
        case ConnectionStatus.ERROR:
          return '连接错误';
        default:
          return '未连接';
      }
    };

    return (
      <HStack className="items-center justify-between mb-3 p-3 bg-background-50 rounded-lg border border-outline-200">
        <HStack className="items-center">
          <Box
            className="w-2 h-2 rounded-full mr-3 border border-outline-300"
            style={{ backgroundColor: getStatusColor() }}
          />
          <UIText className="text-sm text-typography-900 font-medium">SSH: {getStatusText()}</UIText>
        </HStack>

        <HStack>
          {connectionStatus === ConnectionStatus.CONNECTED ? (
            <Button
              className="bg-error-500 px-4 py-2"
              onPress={handleDisconnect}
            >
              <ButtonText className="text-typography-0 text-sm font-medium">断开</ButtonText>
            </Button>
          ) : (
            <Button
              className="bg-primary-500 px-4 py-2"
              onPress={() => setShowConnectionModal(true)}
            >
              <ButtonText className="text-typography-0 text-sm font-medium">连接</ButtonText>
            </Button>
          )}
        </HStack>
      </HStack>
    );
  };

  const renderAIStatus = () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      return null;
    }

    const getAIStatusColor = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '#4CAF50';
        case AIAssistantStatus.PROCESSING:
          return '#FF9800';
        case AIAssistantStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getAIStatusText = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '就绪';
        case AIAssistantStatus.STARTING:
          return '启动中...';
        case AIAssistantStatus.PROCESSING:
          return '处理中...';
        case AIAssistantStatus.ERROR:
          return '错误';
        default:
          return '未启动';
      }
    };

    return (
      <HStack className="items-center justify-between mb-3 p-3 bg-background-50 rounded-lg border border-outline-200">
        <HStack className="items-center">
          <Box
            className="w-2 h-2 rounded-full mr-3 border border-outline-300"
            style={{ backgroundColor: getAIStatusColor() }}
          />
          <UIText className="text-sm text-typography-900 font-medium">AI助手: {getAIStatusText()}</UIText>
        </HStack>

        <HStack>
          {aiAgent ? (
            <Button
              className="bg-error-500 px-4 py-2"
              onPress={stopAIAssistant}
            >
              <ButtonText className="text-typography-0 text-sm font-medium">停止</ButtonText>
            </Button>
          ) : (
            <Button
              className="bg-primary-500 px-4 py-2"
              onPress={startAIAssistant}
            >
              <ButtonText className="text-typography-0 text-sm font-medium">启动</ButtonText>
            </Button>
          )}
        </HStack>
      </HStack>
    );
  };

  const renderTabBar = () => (
    <Box className="bg-background-50 border-b border-outline-200">
      <HStack className="p-1 m-4 bg-background-0 rounded-lg border border-outline-200">
        <Pressable
          className={`flex-1 py-3 px-4 rounded-md ${
            activeTab === 'files' ? 'bg-primary-500' : 'bg-transparent'
          }`}
          onPress={() => setActiveTab('files')}
        >
          <UIText className={`text-center font-medium ${
            activeTab === 'files' ? 'text-typography-0' : 'text-typography-600'
          }`}>
            文件
          </UIText>
        </Pressable>

        <Pressable
          className={`flex-1 py-3 px-4 rounded-md ${
            activeTab === 'chat' ? 'bg-primary-500' : 'bg-transparent'
          }`}
          onPress={() => setActiveTab('chat')}
          disabled={!aiAgent}
        >
          <UIText
            className={`text-center font-medium ${
              activeTab === 'chat'
                ? 'text-typography-0'
                : !aiAgent
                  ? 'text-typography-400'
                  : 'text-typography-600'
            }`}
          >
            AI助手
          </UIText>
        </Pressable>
      </HStack>
    </Box>
  );

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* 头部状态栏 */}
      <VStack className="bg-background-50 p-4 border-b border-outline-200">
        <UIText className="text-xl font-bold text-typography-950 mb-3">远程编码助手</UIText>
        {renderConnectionStatus()}
        {renderAIStatus()}
      </VStack>

      {/* 标签栏 */}
      {renderTabBar()}

      {/* 主内容区域 */}
      <Box className="flex-1">
        {activeTab === 'files' && connectionStatus === ConnectionStatus.CONNECTED && (
          <FileSystemBrowser
            sshService={sshService}
            onFileSelect={handleFileSelect}
            onDirectoryChange={setCurrentPath}
            currentPath={currentPath}
          />
        )}

        {activeTab === 'chat' && aiAgent && (
          <AIAssistantChat
            agent={aiAgent}
            codeContext={codeSelection || undefined}
            filePath={selectedFilePath || undefined}
            onClearContext={handleClearCodeContext}
          />
        )}

        {connectionStatus !== ConnectionStatus.CONNECTED && (
          <Box className="flex-1 justify-center items-center bg-background-50 m-4 rounded-lg border border-outline-200">
            <UIText className="text-base text-typography-600">请先建立SSH连接</UIText>
          </Box>
        )}
      </Box>

      {/* SSH连接模态框 */}
      <SSHConnectionModal
        visible={showConnectionModal}
        onConnect={handleConnect}
        onCancel={() => setShowConnectionModal(false)}
      />

      {/* 代码查看器模态框 */}
      <Modal
        visible={showCodeViewer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedFile && (
          <CodeViewer
            sshService={sshService}
            file={selectedFile}
            onCodeSelect={handleCodeSelect}
            onClose={() => setShowCodeViewer(false)}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};


