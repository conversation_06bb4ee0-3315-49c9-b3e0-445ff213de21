{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "DARK_MODE=media expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@dylankenneally/react-native-ssh-sftp": "^1.5.20", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/toast": "^1.0.9", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "^2.2.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.19", "expo-status-bar": "~2.2.3", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.5", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "^2.27.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.5.2", "react-native-svg": "15.2.0", "react-native-syntax-highlighter": "^2.1.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "jest": "^30.0.4", "jscodeshift": "0.15.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "react-test-renderer": "^19.0.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}