# 远程编码助手设置指南

## 服务器端设置

### 1. 安装Claude Code

在远程Linux服务器上安装Claude Code：

```bash
# 方法1: 使用官方安装脚本（如果可用）
curl -fsSL https://claude.ai/install.sh | bash

# 方法2: 手动安装
# 请参考Claude Code官方文档进行安装
```

### 2. 配置SSH访问

确保SSH服务正在运行：

```bash
sudo systemctl status ssh
sudo systemctl enable ssh
sudo systemctl start ssh
```

### 3. 创建项目目录

```bash
mkdir -p ~/coding-projects
cd ~/coding-projects
```

### 4. 测试Claude Code

```bash
cd ~/coding-projects
claude-code --version
```

## 客户端设置

### 1. SSH连接配置示例

#### 密码认证
```
主机地址: 192.168.1.100
端口: 22
用户名: developer
密码: your_password
```

#### 私钥认证
```
主机地址: your-server.com
端口: 22
用户名: developer
私钥: -----BEGIN OPENSSH PRIVATE KEY-----
       your_private_key_content_here
       -----END OPENSSH PRIVATE KEY-----
私钥密码: (如果私钥有密码)
```

### 2. 生成SSH密钥对（推荐）

在本地生成SSH密钥对：

```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

将公钥复制到服务器：

```bash
ssh-copy-id <EMAIL>
```

### 3. 测试连接

```bash
ssh <EMAIL>
```

## 使用流程

### 1. 建立SSH连接
1. 打开应用
2. 点击"连接"按钮
3. 填写服务器信息
4. 点击"连接"

### 2. 浏览文件系统
1. 连接成功后，会显示文件浏览器
2. 点击文件夹进入子目录
3. 点击"上级目录"返回上层

### 3. 启动AI助手
1. 点击"启动"AI助手按钮
2. 等待Claude Code初始化完成
3. 状态显示为"就绪"时可以开始对话

### 4. 代码上下文交互
1. 在文件浏览器中选择代码文件
2. 在代码查看器中选择特定代码行
3. 点击"确认"将代码作为上下文
4. 在AI助手界面发送消息

## 故障排除

### SSH连接问题

#### 连接超时
- 检查网络连接
- 确认服务器IP地址和端口
- 检查防火墙设置

#### 认证失败
- 验证用户名和密码
- 检查私钥格式和权限
- 确认SSH服务配置

#### 权限被拒绝
- 检查用户权限
- 确认SSH配置允许该用户登录
- 检查公钥是否正确添加到authorized_keys

### AI助手问题

#### Claude Code启动失败
```bash
# 检查Claude Code是否正确安装
which claude-code
claude-code --version

# 检查项目目录权限
ls -la ~/coding-projects
```

#### 命令执行超时
- 检查服务器性能
- 确认Claude Code进程状态
- 重启AI助手

### 应用问题

#### 文件列表加载失败
- 检查SSH连接状态
- 确认目录访问权限
- 重新连接SSH

#### 代码查看器显示异常
- 检查文件编码格式
- 确认文件大小限制
- 尝试刷新文件内容

## 性能优化

### 服务器端
- 使用SSD存储提高文件访问速度
- 确保足够的内存运行Claude Code
- 优化网络连接质量

### 客户端
- 使用稳定的网络连接
- 避免同时打开过多文件
- 定期清理消息历史

## 安全建议

### SSH安全
- 使用强密码或密钥认证
- 定期更换密码和密钥
- 限制SSH访问IP范围
- 禁用root用户SSH登录

### 应用安全
- 不要在公共网络下使用
- 定期更新应用版本
- 保护设备物理安全
- 及时退出登录

## 高级配置

### 自定义Claude Code参数
在AI助手配置中添加额外参数：
```json
{
  "type": "claude-code",
  "projectPath": "/home/<USER>/my-project",
  "additionalArgs": ["--verbose", "--config=/path/to/config"]
}
```

### 多项目支持
为不同项目创建不同的工作目录：
```bash
mkdir -p ~/projects/web-app
mkdir -p ~/projects/mobile-app
mkdir -p ~/projects/api-server
```

### 环境变量配置
设置Claude Code相关环境变量：
```bash
export CLAUDE_CONFIG_PATH=/home/<USER>/.claude
export CLAUDE_LOG_LEVEL=info
```
