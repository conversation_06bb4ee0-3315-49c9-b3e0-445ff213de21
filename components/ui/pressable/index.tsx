'use client';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { tva } from '@gluestack-ui/nativewind-utils/tva';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';

const pressableStyle = tva({
  base: 'opacity-100 active:opacity-80 disabled:opacity-40',
});

type IPressableProps = React.ComponentProps<typeof TouchableOpacity> &
  VariantProps<typeof pressableStyle>;

const Pressable = React.forwardRef<
  React.ComponentRef<typeof TouchableOpacity>,
  IPressableProps
>(({ className, ...props }, ref) => {
  return (
    <TouchableOpacity
      className={pressableStyle({ class: className })}
      {...props}
      ref={ref}
    />
  );
});

Pressable.displayName = 'Pressable';
export { Pressable };
